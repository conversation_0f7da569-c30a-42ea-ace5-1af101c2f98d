{"name": "wallet-service", "version": "1.0.0", "description": "Independent wallet microservice for Coconut backend", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "rimraf dist && tsc", "start": "node dist/server.js"}, "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3", "morgan": "^1.10.0", "zod": "^3.21.4"}, "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.2", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.9", "@types/node": "^20.11.21", "rimraf": "^5.0.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.4.2"}}