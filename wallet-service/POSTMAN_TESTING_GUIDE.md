# Postman Testing Guide for Coconut Wallet Service

## Setup Instructions

### 1. Import Collection and Environment

1. **Open Postman**
2. **Import Collection:**
   - Click "Import" button
   - Select `Coconut-Wallet-Service.postman_collection.json`
   - Click "Import"

3. **Import Environment:**
   - Click "Import" button  
   - Select `Coconut-Wallet-Local.postman_environment.json`
   - Click "Import"

4. **Select Environment:**
   - In the top-right corner, select "Coconut Wallet - Local" environment

### 2. Authentication Setup

Most endpoints require JWT authentication. You'll need to:

1. **Get JWT Token** (from your auth service or generate one)
2. **Set Token in Environment:**
   - Go to Environments → "Coconut Wallet - Local"
   - Set the `authToken` variable with your JWT token
   - Or manually add `Bearer YOUR_TOKEN` in request headers

## Testing Endpoints

### 🟢 Public Endpoints (No Auth Required)

#### 1. Health Check
- **Method:** GET
- **URL:** `/health`
- **Purpose:** Verify service is running
- **Expected Response:**
```json
{
  "success": true,
  "message": "Wallet Service is running",
  "timestamp": "2025-07-28T13:07:29.319Z"
}
```

#### 2. Get All Banks
- **Method:** GET
- **URL:** `/api/v1/wallet/banks`
- **Purpose:** Get list of available banks for transfers
- **Expected Response:** Array of 500+ Nigerian banks

#### 3. Get Wallet Balance
- **Method:** GET
- **URL:** `/api/v1/wallet/balance/{walletId}`
- **Purpose:** Check wallet balance
- **Test with:** `67dc21990a5c933c2d172b11`

#### 4. Get Wallet Transactions
- **Method:** GET
- **URL:** `/api/v1/wallet/transactions/{walletId}`
- **Purpose:** Get transaction history
- **Test with:** `67dc21990a5c933c2d172b11`

#### 5. Verify Transaction
- **Method:** GET
- **URL:** `/api/v1/wallet/verify/{transactionId}`
- **Purpose:** Verify specific transaction
- **Test with:** `****************`

### 🔒 Protected Endpoints (Auth Required)

#### 1. Create Wallet
- **Method:** POST
- **URL:** `/api/v1/wallet/`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
  "businessId": "6853f80e2e4334d4f5f8c858",
  "email": "<EMAIL>",
  "phoneNumber": "+*************",
  "firstName": "John",
  "lastName": "Doe",
  "bvn": "***********",
  "narration": "Test wallet creation",
  "primary": true,
  "businessName": "Test Business"
}
```

#### 2. Process Payment
- **Method:** POST
- **URL:** `/api/v1/wallet/pay`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
  "walletId": "67dc21990a5c933c2d172b11",
  "amount": 100,
  "service": "shipping",
  "meta": {
    "description": "Test payment",
    "reference": "TEST_REF_123"
  }
}
```

#### 3. BANI Transfer to Bank
- **Method:** POST
- **URL:** `/api/v1/wallet/bani`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
  "amount": 1000,
  "bankCode": "044",
  "accountNumber": "**********",
  "narration": "Test transfer via BANI"
}
```

#### 4. Transfer to Bank (Flutterwave)
- **Method:** POST
- **URL:** `/api/v1/wallet/transfer/{walletId}`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
  "amount": 500,
  "bankCode": "044",
  "accountNumber": "**********",
  "narration": "Test transfer via Flutterwave",
  "beneficiaryName": "John Doe"
}
```

#### 5. Update BVN
- **Method:** PATCH
- **URL:** `/api/v1/wallet/bvn`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
  "walletId": "67dc21990a5c933c2d172b11",
  "bvn": "***********"
}
```

#### 6. Get Business Wallets
- **Method:** GET
- **URL:** `/api/v1/wallet/business/{businessId}`
- **Headers:** `Authorization: Bearer {token}`
- **Test with:** `6853f80e2e4334d4f5f8c858`

## Testing Workflow

### 1. Start with Public Endpoints
1. Test **Health Check** to ensure service is running
2. Test **Get All Banks** to verify external API integration
3. Test **Get Wallet Balance** with existing wallet ID
4. Test **Get Wallet Transactions** to see transaction history

### 2. Test Authentication
1. Try a protected endpoint without token (should get 401)
2. Set your JWT token in environment variables
3. Test protected endpoints with valid token

### 3. Test Core Functionality
1. **Create Wallet** (if you have valid business ID)
2. **Process Payment** from wallet
3. **Transfer to Bank** using both BANI and Flutterwave
4. **Update BVN** for wallet
5. **Verify Transaction** status

## Environment Variables

The environment includes these pre-configured variables:

- `baseUrl`: http://localhost:8001
- `authToken`: Your JWT token (set this manually)
- `testBusinessId`: 6853f80e2e4334d4f5f8c858
- `testWalletId`: 67dc21990a5c933c2d172b11
- `testTransactionId`: ****************

## Common Issues & Solutions

### 1. 401 Unauthorized
- **Issue:** Missing or invalid JWT token
- **Solution:** Set valid JWT token in `authToken` environment variable

### 2. 404 Not Found
- **Issue:** Invalid wallet ID or business ID
- **Solution:** Use existing IDs from your database

### 3. 400 Bad Request
- **Issue:** Invalid request body or missing required fields
- **Solution:** Check request body matches schema requirements

### 4. Service Not Running
- **Issue:** Connection refused
- **Solution:** Ensure wallet service is running on port 8001

## Bank Codes Reference

Common Nigerian bank codes for testing transfers:
- Access Bank: 044
- GTBank: 058
- First Bank: 011
- Zenith Bank: 057
- UBA: 033
- Fidelity Bank: 070

## Next Steps

1. Import the collection and environment files
2. Start with public endpoints to verify connectivity
3. Set up authentication token
4. Test protected endpoints
5. Monitor responses and debug any issues

The service is ready for testing! 🚀
