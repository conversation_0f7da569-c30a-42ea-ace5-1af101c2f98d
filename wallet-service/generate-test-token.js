#!/usr/bin/env node

const jwt = require('jsonwebtoken');
require('dotenv').config();

// Test user payload
const testUser = {
  id: '6853f80e2e4334d4f5f8c858',
  email: '<EMAIL>',
  role: 'user',
  firstName: 'Test',
  lastName: 'User'
};

// Generate token
const token = jwt.sign(testUser, process.env.JWT_SECRET, { 
  expiresIn: '24h' 
});

console.log('🔑 Test JWT Token Generated:');
console.log('');
console.log(token);
console.log('');
console.log('📋 Copy this token and use it in Postman:');
console.log(`Authorization: Bearer ${token}`);
console.log('');
console.log('⏰ Token expires in 24 hours');
console.log('');
console.log('🧪 Test user details:');
console.log(JSON.stringify(testUser, null, 2));
