# Wallet Service

Independent wallet microservice for Coconut backend services.

## Overview

This is a standalone wallet service that handles all wallet-related operations including:
- Wallet creation and management
- Payment processing
- Bank transfers (via Flutterwave and Bani)
- Transaction tracking
- Balance management

## Features

- **Wallet Management**: Create and manage business wallets
- **Payment Processing**: Process payments for various services (shipping, filling, packaging, transfer)
- **Bank Integration**: Integration with Flutterwave and Bani payment gateways
- **Transaction History**: Track all wallet transactions
- **Balance Management**: Real-time balance updates
- **BVN Updates**: Update Bank Verification Numbers
- **Security**: JWT authentication and request validation

## API Endpoints

### Base URL: `http://localhost:8001/api/v1/wallet`

- `POST /` - Create a new wallet
- `GET /banks` - Get all available banks
- `POST /pay` - Process payment from wallet
- `POST /bani` - Transfer money via Bani
- `POST /transfer/:walletId` - Transfer money via Flutterwave
- `GET /transactions/:walletId` - Get wallet transactions
- `GET /verify/:transactionId` - Verify a transaction
- `PATCH /bvn` - Update wallet BVN
- `GET /business/:businessId` - Get business wallets
- `GET /balance/:walletId` - Get wallet balance

## Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
Copy `.env.example` to `.env` and fill in the required values.

3. Start the service:
```bash
# Development
npm run dev

# Production
npm run build
npm start
```

## Environment Variables

- `PORT` - Service port (default: 8001)
- `MONGO_URI` - MongoDB connection string
- `JWT_SECRET` - JWT secret key
- `FLUTTERWAVE_BASE_URL` - Flutterwave API base URL
- `FLUTTERWAVE_SECRET_KEY` - Flutterwave secret key
- `BANI_BASE_URL` - Bani API base URL
- `BANI_TOKEN` - Bani authentication token
- `BANI_MONI_SIGNATURE` - Bani signature
- `BANI_SHARED_KEY` - Bani shared key
- Wallet IDs for different services

## Architecture

The service follows a clean architecture pattern:
- **Routes**: Handle HTTP requests and responses
- **Controllers**: Business logic coordination
- **Services**: Core business logic and external API integration
- **Models**: Database schema definitions
- **Middleware**: Authentication, validation, and error handling
- **Utils**: Helper functions and utilities

## Database Models

- **Wallet**: Stores wallet information and balances
- **Transaction**: Records all wallet transactions
- **Business**: Business information (referenced)
- **User**: User information (referenced)

## Security

- JWT authentication for protected routes
- Request validation using Zod schemas
- Helmet for security headers
- CORS enabled for cross-origin requests

## Monitoring

- Health check endpoint: `GET /health`
- Request logging with Morgan
- Error handling and logging
