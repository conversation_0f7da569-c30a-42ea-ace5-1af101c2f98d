import { z } from "zod";

export const createWalletSchema = z.object({
  businessId: z.string().min(1, "Business ID is required"),
  email: z.string().min(1, "Email is required"),
  phoneNumber: z.string().min(1, "Phone is required"),
  firstName: z.string().min(1, "First Name is required"),
  lastName: z.string().min(1, "Last Name is required"),
  narration: z.string().min(1, "Narration is required"),
  bvn: z.string().min(1, "BVN is required"),
  currency: z.string().min(1, "Currency is required"),
});

export const processPaymentSchema = z.object({
  walletId: z.string().min(1, "Wallet ID is required"),
  amount: z.array(z.number().positive("Amount must be positive")),
  service: z.enum(["shipping", "filling", "packaging", "transfer"]),
  meta: z.string().min(1, "Meta information is required"),
});

export const updateBvnSchema = z.object({
  walletId: z.string().min(1, "Wallet ID is required"),
  bvn: z.string().min(11, "BVN must be at least 11 characters").max(11, "BVN must be exactly 11 characters"),
});

export const transferToBankSchema = z.object({
  recipientAccountBank: z.string().min(1, "Recipient bank is required"),
  recipientAccountNumber: z.string().min(1, "Recipient account number is required"),
  amount: z.number().positive("Amount must be positive"),
  narration: z.string().min(1, "Narration is required"),
});

export const getTransactionsByWalletId = z.object({
  walletId: z.string().min(1, "Wallet ID is required"),
});
