import { Request, Response } from "express";
import { WalletService } from "../services/walletService";
import { User } from "../models/User";

const walletService = new WalletService();

interface AuthRequest extends Request {
  user?: any;
}

export class WalletController {
  /**
   * Create a new wallet for a business
   */
  async createWallet(req: AuthRequest, res: Response) {
    try {
      const {
        businessId,
        email,
        phoneNumber,
        firstName,
        lastName,
        bvn,
        narration,
        primary,
        businessName,
      } = req.body;

      const wallet = await walletService.createWallet(
        businessId,
        email,
        phoneNumber,
        firstName,
        lastName,
        bvn,
        narration,
        primary,
        businessName
      );

      res.status(201).json({
        success: true,
        message: "Wallet created successfully",
        data: wallet,
      });
    } catch (error: any) {
      console.error("Error creating wallet:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Get all banks from Flutterwave
   */
  async getAllBanks(req: Request, res: Response) {
    try {
      const { countryCode } = req.query;
      const banks = await walletService.getAllBanks(countryCode as string);

      res.status(200).json({
        success: true,
        message: "Banks fetched successfully",
        data: banks,
      });
    } catch (error: any) {
      console.error("Error fetching banks:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Process payment from wallet
   */
  async processPayment(req: AuthRequest, res: Response) {
    try {
      const { walletId, amount, service, meta } = req.body;

      const result = await walletService.processPayment(
        walletId,
        amount,
        meta,
        service
      );

      res.status(200).json({
        success: true,
        message: "Payment processed successfully",
        data: result,
      });
    } catch (error: any) {
      console.error("Error processing payment:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Transfer money to bank account via Bani
   */
  async baniTransferToBank(req: AuthRequest, res: Response) {
    try {
      const {
        receiverAmount,
        transferMethod,
        receiverCurrency,
        transferReceiverType,
        receiverAccountNumber,
        receiverCountryCode,
        receiverAccountName,
        receiverSortCode,
        senderAmount,
        senderCurrency,
        businessId,
        narration,
        meta,
        walletId,
      } = req.body;

      const result = await walletService.baniTransferToBank(
        receiverAmount,
        transferMethod,
        receiverCurrency,
        transferReceiverType,
        receiverAccountNumber,
        receiverCountryCode,
        receiverAccountName,
        receiverSortCode,
        senderAmount,
        senderCurrency,
        businessId,
        narration,
        meta,
        walletId
      );

      res.status(200).json({
        success: true,
        message: "Transfer initiated successfully",
        data: result,
      });
    } catch (error: any) {
      console.error("Error processing transfer:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Transfer money to bank account via Flutterwave
   */
  async transferToBank(req: AuthRequest, res: Response) {
    try {
      const { recipientAccountBank, recipientAccountNumber, amount, narration } =
        req.body;
      const { walletId } = req.params;

      const result = await walletService.transferToBank(
        walletId,
        recipientAccountBank,
        recipientAccountNumber,
        amount,
        narration
      );

      res.status(200).json({
        success: true,
        message: "Transfer initiated successfully",
        data: result,
      });
    } catch (error: any) {
      console.error("Error processing transfer:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Get transactions by wallet ID
   */
  async getTransactionsByWalletId(req: Request, res: Response) {
    try {
      const { walletId } = req.params;
      const transactions = await walletService.getTransactionsByWalletId(
        walletId
      );

      res.status(200).json({
        success: true,
        message: "Transactions fetched successfully",
        data: transactions,
      });
    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Verify a transaction
   */
  async verifyTransaction(req: Request, res: Response) {
    try {
      const { transactionId } = req.params;
      const result = await walletService.verifyTransaction(transactionId);

      res.status(200).json({
        success: true,
        message: "Transaction verified successfully",
        data: result,
      });
    } catch (error: any) {
      console.error("Error verifying transaction:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Update BVN for a wallet
   */
  async updateBvn(req: Request, res: Response) {
    try {
      const { walletId, bvn } = req.body;
      const result = await walletService.updateBvn(walletId, bvn);

      res.status(200).json({
        success: true,
        message: "BVN updated successfully",
        data: result,
      });
    } catch (error: any) {
      console.error("Error updating BVN:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Get business wallets
   */
  async getBusinessWallets(req: AuthRequest, res: Response) {
    try {
      const { businessId } = req.params;
      const wallets = await walletService.getBusinessWallets(businessId);

      res.status(200).json({
        success: true,
        message: "Wallets fetched successfully",
        data: wallets,
      });
    } catch (error: any) {
      console.error("Error fetching wallets:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Get wallet balance
   */
  async getWalletBalance(req: Request, res: Response) {
    try {
      const { walletId } = req.params;
      const balance = await walletService.getWalletBalance(walletId);

      res.status(200).json({
        success: true,
        message: "Balance fetched successfully",
        data: balance,
      });
    } catch (error: any) {
      console.error("Error fetching balance:", error);
      res.status(400).json({
        success: false,
        message: error.message,
      });
    }
  }
}
