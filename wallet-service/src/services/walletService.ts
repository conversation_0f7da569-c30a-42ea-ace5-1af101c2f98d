import axios from "axios";
import { Wallet } from "../models/Wallet";
import { env } from "../utils/envValidator";
import { Business } from "../models/Business";
import mongoose from "mongoose";
import { Transaction } from "../models/Transaction";
import { generateTxId } from "../utils/otp";
import {flattenObject} from "../utils/flatten";

export class WalletService {
  private flutterwaveBaseUrl = env.FLUTTERWAVE_BASE_URL;
  private shippingWalletId = env.SHIPPING_WALLET_ID;
  private packagingWalletId = env.PACKAGING_WALLET_ID;
  private fillingWalletId = env.FILLING_WALLET_ID;
  private bankingWalletId = env.BANKING_WALLET_ID;
  private transferWalletId = env.TRANSFER_WALLET_ID;
  private flutterwaveSecret = env.FLUTTERWAVE_SECRET_KEY as string;
  private baniBaseUrl = env.BANI_BASE_URL;
  private moniSignature = env.BANI_MONI_SIGNATURE;
  private baniToken = env.BANI_TOKEN;

  async createWallet(
    businessId: string,
    email: string,
    phoneNumber: string,
    firstName: string,
    lastName: string,
    bvn: string,
    narration: string,
    primary?: boolean,
    businessName? : string
  ) {
    try {
      // **Validate businessId format**
      if (!mongoose.Types.ObjectId.isValid(businessId)) {
        throw new Error("Invalid businessId format");
      }

      // // // **Check if business exists**
      const businessExists = await Business.findById(businessId);
      if (!businessExists) {
        throw new Error("Business not found");
      }

      // **Check if wallet already exists for this business**
      const existingWallet = await Wallet.findOne({ businessId });
      if (existingWallet) {
        throw new Error("Wallet already exists for this business");
      }

      // **Generate unique transaction reference**
      const txRef = `Coconut_${Date.now()}`;
      const customer_ref = `customer_${Date.now()}`;

      const walletPayload = {
        pay_va_step: "direct",
        country_code: "NG",
        pay_currency: "NGN",
        holder_account_type: "permanent",
        customer_ref,
        pay_ext_ref: txRef,
        alternate_name: businessName? businessName : firstName + " " + lastName,
        holder_legal_number: bvn,
        bank_name: "guaranty trust bank",
      }

      console.log("creating new bank")

      const walletResponse = await axios.post(
        `${this.baniBaseUrl}partner/collection/bank_transfer/`,
        walletPayload,
        {
          headers: { 
            Authorization: `Bearer ${this.baniToken}`,
            "Content-Type": "application/json",
            "moni-signature": this.moniSignature,
          },
        }
      );

      const responseData = walletResponse.data;
      console.log("Response from Bani:", responseData);

      if (!responseData.success) {
        throw new Error("Failed to create wallet with Bani");
      }

      const {
        holder_account_number,
        holder_bank_name,
      } = responseData.data;

      // Create and save the wallet in MongoDB
      const wallet = await Wallet.create({
        businessId,
        balance: 0,
        currency: "NGN", // Change if dynamic
        narration,
        txRef,
        flwRef: txRef,
        orderRef: txRef,
        accountNumber: Number(holder_account_number),
        accountStatus: "ACTIVE",
        bankName: holder_bank_name,
        primary,
        firstName,
        lastName,
        email, //Use the Uncorrupted email here
        phoneNumber,
        createdAt: new Date(),
      });

      return wallet;

    } catch (error: any) {
      console.log("error somewhere")
      console.log((error))
      throw new Error(
        `Error creating wallet: ${error?.response?.data?.message}`
      );
    }
  }

  /**
   * Get all bank details from Flutterwave
   */
  async getAllBanks(countryCode: string = "NG") {
    try {
      const response = await axios.get(
        `${this.flutterwaveBaseUrl}/banks/${countryCode}`,
        {
          headers: {
            Authorization: `Bearer ${this.flutterwaveSecret}`,
          },
        }
      );

      if (response.data.status !== "success") {
        throw new Error("Failed to fetch banks");
      }

      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching banks:", error.response?.data || error);
      throw new Error(
        `Failed to fetch banks: ${
          error?.response?.data?.message || "Unknown error"
        }`
      );
    }
  }

  async getBusinessWallets(businessId: string) {
    return Wallet.find({ businessId });
  }

  async getWalletBalance(walletId: string) {
    const wallet = await Wallet.findById(walletId);
    if (!wallet) throw new Error("Wallet not found");
    return { balance: wallet.balance };
  }

  async processPayment(
    walletId: string,
    amount: number[],
    meta: string,
    service: "shipping" | "filling" | "packaging" | "transfer"
  ) {
    const wallet = await Wallet.findById(walletId);
    console.log(walletId)

    if (!wallet) throw new Error("Wallet not found");

    const total = amount.reduce((prev, curr) => prev + curr, 0);
    if (wallet.balance < total) throw new Error("Insufficient funds");      

    // Deduct balance       
    wallet.balance -= total;
    await wallet.save();

    // Generate transactionId               
    const txId = generateTxId();

    // User wallet transaction
    const transactions = [
      {
        txId,
        txRef: wallet.txRef,
        walletId,
        amount: total,
        currency: wallet.currency,
        type: "payment",
        meta,
        status: "successful",
      },
    ];

    // Service wallet transactions
    const serviceWallets = {
      shipping: this.shippingWalletId,
      filling: this.fillingWalletId,
      packaging: this.packagingWalletId,
      transfer: this.transferWalletId,
    };

    const serviceWalletId = serviceWallets[service];
    if (serviceWalletId) {
      const serviceWallet = await Wallet.findById(serviceWalletId);
      if (serviceWallet) {
        serviceWallet.balance += total;
        await serviceWallet.save();

        transactions.push({
          txId: generateTxId(),
          txRef: serviceWallet.txRef,
          walletId: serviceWalletId,
          amount: total,
          currency: serviceWallet.currency,
          type: "funding",
          meta,
          status: "successful",
        });
      }
    }

    // Save all transactions
    await Transaction.insertMany(transactions);

    return { success: true, txId, amount: total };
  }

  async transferToBank(
    walletId: string,
    recipientAccountBank: string,
    recipientAccountNumber: string,
    amount: number,
    narration: string
  ) {
    const wallet = await Wallet.findById(walletId);
    if (!wallet) throw new Error("Wallet not found");

    if (wallet.balance < amount) throw new Error("Insufficient funds");

    const transferData = {
      account_bank: recipientAccountBank,
      account_number: recipientAccountNumber,
      amount,
      narration,
      currency: "NGN",
      reference: `transfer_${Date.now()}`,
      callback_url: "https://webhook.site/b3e505b0-fe02-430c-a538-d743a55389c7",
      debit_currency: "NGN",
    };

    try {
      const response = await axios.post(
        `${this.flutterwaveBaseUrl}/transfers`,
        transferData,
        {
          headers: {
            Authorization: `Bearer ${this.flutterwaveSecret}`,
          },
        }
      );

      if (response.data.status !== "success") {
        throw new Error("Transfer failed");
      }

      // Deduct from wallet
      wallet.balance -= amount;
      await wallet.save();

      // Record transaction
      const txId = generateTxId();
      await Transaction.create({
        txId,
        txRef: wallet.txRef,
        walletId,
        amount,
        currency: wallet.currency,
        type: "payment",
        meta: { narration, recipientAccountBank, recipientAccountNumber },
        status: "successful",
      });

      return response.data;
    } catch (error: any) {
      throw new Error(`Transfer failed: ${error?.response?.data?.message}`);
    }
  }

  async baniTransferToBank(
    receiverAmount: number,
    transferMethod: string,
    receiverCurrency: string,
    transferReceiverType: string,
    receiverAccountNumber: string,
    receiverCountryCode: string,
    receiverAccountName: string,
    receiverSortCode: string,
    senderAmount: number,
    senderCurrency: string,
    businessId: string,
    narration: string,
    meta: any,
    walletId: string
  ) {
    try {
      const transferPayload = {
        receiver_amount: receiverAmount,
        transfer_method: transferMethod,
        receiver_currency: receiverCurrency,
        transfer_receiver_type: transferReceiverType,
        receiver_account_number: receiverAccountNumber,
        receiver_country_code: receiverCountryCode,
        receiver_account_name: receiverAccountName,
        receiver_sort_code: receiverSortCode,
        sender_amount: senderAmount,
        sender_currency: senderCurrency,
        business_id: businessId,
        narration,
        meta: flattenObject(meta),
      };

      const response = await axios.post(
        `${this.baniBaseUrl}partner/transfer/`,
        transferPayload,
        {
          headers: {
            Authorization: `Bearer ${this.baniToken}`,
            "Content-Type": "application/json",
            "moni-signature": this.moniSignature,
          },
        }
      );

      // Record transaction
      const txId = generateTxId();
      await Transaction.create({
        txId,
        txRef: `transfer_${Date.now()}`,
        walletId,
        amount: senderAmount,
        currency: senderCurrency,
        type: "payment",
        meta: transferPayload,
        status: "successful",
      });

      return response.data;
    } catch (error: any) {
      throw new Error(`Bani transfer failed: ${error?.response?.data?.message}`);
    }
  }

  async getTransactionsByWalletId(walletId: string) {
    return Transaction.find({ walletId }).sort({ createdAt: -1 });
  }

  async verifyTransaction(transactionId: string) {
    try {
      const response = await axios.get(
        `${this.flutterwaveBaseUrl}/transactions/${transactionId}/verify`,
        {
          headers: {
            Authorization: `Bearer ${this.flutterwaveSecret}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      throw new Error(`Verification failed: ${error?.response?.data?.message}`);
    }
  }

  /**
   * Update BVN for a Wallet via Flutterwave
   */
  async updateBvn(walletId: string, newBvn: string): Promise<any> {
    try {
      const wallet = await Wallet.findById(walletId);
      if (!wallet) throw new Error("Wallet not found");

      const requestData = {
        order_ref: wallet.orderRef,
        bvn: newBvn,
      };

      const response = await axios.post(
        `${this.flutterwaveBaseUrl}/virtual-account-numbers/update-bvn`,
        requestData,
        {
          headers: {
            Authorization: `Bearer ${this.flutterwaveSecret}`,
          },
        }
      );

      const responseData = response.data;
      if (responseData.status !== "success") {
        throw new Error("Failed to update BVN");
      }

      return responseData;
    } catch (error: any) {
      console.error("Error updating BVN:", error);
      throw new Error(
        `Failed to update BVN: ${error?.response?.data?.message}`
      );
    }
  }
}
