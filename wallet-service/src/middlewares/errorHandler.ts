import { Request, Response, NextFunction } from "express";
import { ZodError } from "zod";
import mongoose from "mongoose";

export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error("🔥 Error:", err.message);

  let status = 500;
  let message = "Internal Server Error";
  let errors: any = undefined;

  // 🔹 Handle Zod Validation Errors (for request body validation)
  if (err instanceof ZodError) {
    status = 400;
    message = "Validation Error";
    errors = err.flatten().fieldErrors;
  }

  // 🔹 Handle Mongoose Validation Errors
  else if (err instanceof mongoose.Error.ValidationError) {
    status = 400;
    message = "Mongoose Validation Error";
    errors = Object.values(err.errors).map((e: any) => e.message);
  }

  // 🔹 Handle Mongoose Cast Errors (Invalid ObjectId)
  else if (err instanceof mongoose.Error.CastError) {
    status = 400;
    message = `Invalid ${err.path}: ${err.value}`;
  }

  // 🔹 Handle Mongoose Duplicate Key Errors
  else if (err.code === 11000) {
    status = 400;
    const field = Object.keys(err.keyValue)[0];
    message = `${field} already exists`;
  }

  // 🔹 Handle JWT Errors
  else if (err.name === "JsonWebTokenError") {
    status = 401;
    message = "Invalid token";
  } else if (err.name === "TokenExpiredError") {
    status = 401;
    message = "Token expired";
  }

  // 🔹 Handle Custom Application Errors
  else if (err.statusCode) {
    status = err.statusCode;
    message = err.message;
  }

  res.status(status).json({
    success: false,
    message,
    ...(errors && { errors }),
    ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
  });
};
