import { Router } from "express";
import { WalletController } from "../controllers/walletController";
import { protect } from "../middlewares/auth";
import { validate } from "../middlewares/validate";
import {
  createWalletSchema,
  processPaymentSchema,
  updateBvnSchema,
  transferToBankSchema,
  getTransactionsByWalletId,
} from "../schemas/walletSchema";

const router = Router();
const walletController = new WalletController();

// Create wallet
router.post(
  "/",
  protect,
  validate({ body: createWalletSchema }),
  walletController.createWallet
);

// Get all banks
router.get("/banks", walletController.getAllBanks);

// Process payment
router.post(
  "/pay",
  protect,
  validate({ body: processPaymentSchema }),
  walletController.processPayment
);

// Bani transfer to bank
router.post("/bani", protect, walletController.baniTransferToBank);

// Transfer to bank via Flutterwave
router.post(
  "/transfer/:walletId",
  protect,
  validate({ body: transferToBankSchema }),
  walletController.transferToBank
);

// Get transactions by wallet ID
router.get(
  "/transactions/:walletId",
  validate({ params: getTransactionsByWalletId }),
  walletController.getTransactionsByWalletId
);

// Verify transaction
router.get("/verify/:transactionId", walletController.verifyTransaction);

// Update BVN
router.patch(
  "/bvn",
  protect,
  validate({ body: updateBvnSchema }),
  walletController.updateBvn
);

// Get business wallets
router.get("/business/:businessId", protect, walletController.getBusinessWallets);

// Get wallet balance
router.get("/balance/:walletId", walletController.getWalletBalance);

export default router;
