import dotenv from "dotenv";
dotenv.config();

import app from "./app";
import { env } from "./utils/envValidator";

const PORT = env.PORT || 8001;

const server = app.listen(PORT, () => {
  console.log(`🚀 Wallet Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api/v1/wallet`);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received. Shutting down gracefully...");
  server.close(() => {
    console.log("Process terminated");
  });
});

process.on("SIGINT", () => {
  console.log("SIGINT received. Shutting down gracefully...");
  server.close(() => {
    console.log("Process terminated");
  });
});

export default server;
