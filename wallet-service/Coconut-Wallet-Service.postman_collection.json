{"info": {"name": "Coconut Wallet Service", "description": "API collection for testing Coconut Wallet Service endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8001", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}, "response": []}, {"name": "Get All Banks", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/wallet/banks", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "banks"]}}, "response": []}, {"name": "Create Wallet", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"businessId\": \"686c036e17387f9b8f51a948\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"+*************\",\n  \"firstName\": \"Test\",\n  \"lastName\": \"User\",\n  \"bvn\": \"***********\",\n  \"narration\": \"Test wallet for KinsTech NG\",\n  \"primary\": false,\n  \"businessName\": \"KinsTech NG\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/wallet/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", ""]}}, "response": []}, {"name": "Process Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"walletId\": \"686c03af17387f9b8f51a95c\",\n  \"amount\": [100],\n  \"service\": \"shipping\",\n  \"meta\": \"Test payment from <PERSON> wallet (₦26,200 balance)\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/wallet/pay", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "pay"]}}, "response": []}, {"name": "BANI Transfer to Bank", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 1000,\n  \"bankCode\": \"044\",\n  \"accountNumber\": \"**********\",\n  \"narration\": \"Test transfer via BANI\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/wallet/bani", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "bani"]}}, "response": []}, {"name": "Transfer to Bank (Flutterwave)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 500,\n  \"bankCode\": \"044\",\n  \"accountNumber\": \"**********\",\n  \"narration\": \"Test transfer via Flutterwave\",\n  \"beneficiary<PERSON>ame\": \"<PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/wallet/transfer/67dc21990a5c933c2d172b11", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "transfer", "67dc21990a5c933c2d172b11"]}}, "response": []}, {"name": "Get Wallet Transactions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/wallet/transactions/67dc21990a5c933c2d172b11", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "transactions", "67dc21990a5c933c2d172b11"]}}, "response": []}, {"name": "Verify Transaction", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/wallet/verify/****************", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "verify", "****************"]}}, "response": []}, {"name": "Update BVN", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"walletId\": \"67dc21990a5c933c2d172b11\",\n  \"bvn\": \"***********\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/wallet/bvn", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "bvn"]}}, "response": []}, {"name": "Get Business Wallets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/wallet/business/6853f80e2e4334d4f5f8c858", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "business", "6853f80e2e4334d4f5f8c858"]}}, "response": []}, {"name": "Get Wallet Balance", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/wallet/balance/67dc21990a5c933c2d172b11", "host": ["{{baseUrl}}"], "path": ["api", "v1", "wallet", "balance", "67dc21990a5c933c2d172b11"]}}, "response": []}]}