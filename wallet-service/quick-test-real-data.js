#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:8001';

// Real wallet IDs from your database
const REAL_WALLETS = {
  coconutBanking: '67dc21990a5c933c2d172b11',    // ₦760
  jenkins: '686c03af17387f9b8f51a95c',           // ₦26,200  
  coconutTransfers: '686f80b53ec2579eeec397c1',  // ₦4,122,578
  precious: '686fb813a9202abdc1e7ec99'           // ₦1,500
};

const REAL_BUSINESS_IDS = {
  kinstech: '686c036e17387f9b8f51a948',
  ogEnterprise: '686e43186e5eff08d3493466'
};

const REAL_TRANSACTION_ID = '****************';

async function testRealData() {
  console.log('🧪 Testing Wallet Service with REAL DATABASE DATA');
  console.log('=' .repeat(60));
  
  try {
    // 1. Health Check
    console.log('\n1. 🏥 Health Check...');
    const health = await axios.get(`${BASE_URL}/health`);
    console.log(`✅ Status: ${health.data.message}`);
    
    // 2. Get Banks
    console.log('\n2. 🏦 Getting Banks...');
    const banks = await axios.get(`${BASE_URL}/api/v1/wallet/banks`);
    console.log(`✅ Found ${banks.data.data.length} banks`);
    
    // 3. Check Real Wallet Balances
    console.log('\n3. 💰 Checking Real Wallet Balances...');
    
    for (const [name, walletId] of Object.entries(REAL_WALLETS)) {
      try {
        const balance = await axios.get(`${BASE_URL}/api/v1/wallet/balance/${walletId}`);
        console.log(`✅ ${name}: ₦${balance.data.data.balance.toLocaleString()}`);
      } catch (error) {
        console.log(`❌ ${name}: Error - ${error.response?.data?.message || error.message}`);
      }
    }
    
    // 4. Get Transaction History
    console.log('\n4. 📊 Getting Transaction History (Coconut Banking)...');
    try {
      const transactions = await axios.get(`${BASE_URL}/api/v1/wallet/transactions/${REAL_WALLETS.coconutBanking}`);
      console.log(`✅ Found ${transactions.data.data.length} transactions`);
      if (transactions.data.data.length > 0) {
        const latest = transactions.data.data[0];
        console.log(`   Latest: ₦${latest.amount} - ${latest.status} - ${latest.type}`);
      }
    } catch (error) {
      console.log(`❌ Transaction history error: ${error.response?.data?.message || error.message}`);
    }
    
    // 5. Verify Real Transaction
    console.log('\n5. ✅ Verifying Real Transaction...');
    try {
      const verification = await axios.get(`${BASE_URL}/api/v1/wallet/verify/${REAL_TRANSACTION_ID}`);
      console.log(`✅ Transaction verified: ₦${verification.data.data.amount} - ${verification.data.data.status}`);
    } catch (error) {
      console.log(`❌ Transaction verification error: ${error.response?.data?.message || error.message}`);
    }
    
    // 6. Test Protected Endpoint (without auth - should fail)
    console.log('\n6. 🔒 Testing Protected Endpoint (should fail without auth)...');
    try {
      await axios.get(`${BASE_URL}/api/v1/wallet/business/${REAL_BUSINESS_IDS.kinstech}`);
      console.log('❌ Unexpected: Protected endpoint allowed access without auth');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Correctly blocked unauthorized access');
      } else {
        console.log(`❌ Unexpected error: ${error.response?.data?.message || error.message}`);
      }
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 SUMMARY:');
    console.log('✅ Service is running with REAL database data');
    console.log('✅ All public endpoints working');
    console.log('✅ Authentication protection active');
    console.log('✅ Real wallet balances accessible');
    console.log('✅ Transaction history available');
    console.log('\n🚀 Ready for Postman testing with real data!');
    console.log('\n⚠️  CAUTION: This is LIVE data - be careful with transactions!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the wallet service is running on port 8001');
      console.log('   Run: npm run dev');
    }
  }
}

testRealData();
