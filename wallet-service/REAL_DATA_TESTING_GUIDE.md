# 🧪 Real Data Testing Guide - Coconut Wallet Service

## 🎯 **Setup Instructions**

### 1. **Import Updated Files**
- Import `Coconut-Wallet-Service.postman_collection.json`
- Import `Coconut-Wallet-Local.postman_environment.json` (updated with real IDs)
- Select "Coconut Wallet - Local" environment

### 2. **Authentication Token**
Use this JWT token (valid for 24 hours):
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************.bhjZg3_opBpNDA0ds_diyv52CjtCzSjfEoAg6ajYCmA
```

Set this in your environment variable `authToken` or use:
```
Authorization: Bearer [TOKEN_ABOVE]
```

## 📊 **Real Database Data**

### **Available Wallets:**
| Name | Wallet ID | Balance | Business |
|------|-----------|---------|----------|
| Coconut Banking | `67dc21990a5c933c2d172b11` | ₦760 | System |
| Jenkins Uwagbai | `686c03af17387f9b8f51a95c` | ₦26,200 | KinsTech NG |
| Coconut Transfers | `686f80b53ec2579eeec397c1` | ₦4,122,578 | System |
| Precious Oguname | `686fb813a9202abdc1e7ec99` | ₦1,500 | OG-Entreprise |

### **Available Businesses:**
| Business ID | Name | Owner |
|-------------|------|-------|
| `686c036e17387f9b8f51a948` | KinsTech NG | Jenkins Uwagbai |
| `686e43186e5eff08d3493466` | OG-Entreprise | Precious Oguname |

### **Recent Transactions:**
| Transaction ID | Amount | Status |
|----------------|--------|--------|
| `****************` | ₦50 | successful |
| `****************` | ₦22,220 | successful |

## 🧪 **Test Cases with Real Data**

### **🟢 1. Health Check**
**Method:** GET  
**URL:** `/health`  
**Expected:** Service status with timestamp

---

### **🟢 2. Get All Banks**
**Method:** GET  
**URL:** `/api/v1/wallet/banks`  
**Expected:** 500+ Nigerian banks from Flutterwave

---

### **🟢 3. Get Wallet Balance - Coconut Banking**
**Method:** GET  
**URL:** `/api/v1/wallet/balance/67dc21990a5c933c2d172b11`  
**Expected Response:**
```json
{
  "success": true,
  "message": "Balance fetched successfully",
  "data": {
    "balance": 760
  }
}
```

---

### **🟢 4. Get Wallet Balance - Jenkins (Rich Wallet)**
**Method:** GET  
**URL:** `/api/v1/wallet/balance/686c03af17387f9b8f51a95c`  
**Expected Response:**
```json
{
  "success": true,
  "message": "Balance fetched successfully",
  "data": {
    "balance": 26200
  }
}
```

---

### **🟢 5. Get Transaction History**
**Method:** GET  
**URL:** `/api/v1/wallet/transactions/67dc21990a5c933c2d172b11`  
**Expected:** List of transactions for Coconut Banking wallet

---

### **🟢 6. Verify Existing Transaction**
**Method:** GET  
**URL:** `/api/v1/wallet/verify/****************`  
**Expected:** Transaction details for ₦50 payment

---

### **🔒 7. Process Payment (Auth Required)**
**Method:** POST  
**URL:** `/api/v1/wallet/pay`  
**Headers:** `Authorization: Bearer [TOKEN]`  
**Body:**
```json
{
  "walletId": "686c03af17387f9b8f51a95c",
  "amount": [100],
  "service": "shipping",
  "meta": "Test payment from Jenkins wallet"
}
```
**Note:** Uses Jenkins' wallet (₦26,200 balance)

---

### **🔒 8. Create New Wallet (Auth Required)**
**Method:** POST  
**URL:** `/api/v1/wallet/`  
**Headers:** `Authorization: Bearer [TOKEN]`  
**Body:**
```json
{
  "businessId": "686c036e17387f9b8f51a948",
  "email": "<EMAIL>",
  "phoneNumber": "+*************",
  "firstName": "Test",
  "lastName": "User",
  "bvn": "***********",
  "narration": "Test wallet for KinsTech NG",
  "primary": false,
  "businessName": "KinsTech NG"
}
```
**Note:** Creates wallet for existing KinsTech NG business

---

### **🔒 9. Get Business Wallets (Auth Required)**
**Method:** GET  
**URL:** `/api/v1/wallet/business/686c036e17387f9b8f51a948`  
**Headers:** `Authorization: Bearer [TOKEN]`  
**Expected:** All wallets for KinsTech NG business

---

### **🔒 10. Transfer to Bank - Flutterwave (Auth Required)**
**Method:** POST  
**URL:** `/api/v1/wallet/transfer/686c03af17387f9b8f51a95c`  
**Headers:** `Authorization: Bearer [TOKEN]`  
**Body:**
```json
{
  "recipientAccountBank": "044",
  "recipientAccountNumber": "**********",
  "amount": 500,
  "narration": "Test transfer from Jenkins wallet"
}
```
**Note:** Transfer from Jenkins' wallet (₦26,200 balance)

---

### **🔒 11. BANI Transfer (Auth Required)**
**Method:** POST  
**URL:** `/api/v1/wallet/bani`  
**Headers:** `Authorization: Bearer [TOKEN]`  
**Body:**
```json
{
  "amount": 1000,
  "bankCode": "044",
  "accountNumber": "**********",
  "narration": "Test BANI transfer"
}
```

---

### **🔒 12. Update BVN (Auth Required)**
**Method:** PATCH  
**URL:** `/api/v1/wallet/bvn`  
**Headers:** `Authorization: Bearer [TOKEN]`  
**Body:**
```json
{
  "walletId": "686c03af17387f9b8f51a95c",
  "bvn": "***********"
}
```

## 🎯 **Recommended Testing Order**

### **Phase 1: Public Endpoints (No Auth)**
1. ✅ Health Check
2. ✅ Get All Banks  
3. ✅ Get Wallet Balance (Coconut Banking - ₦760)
4. ✅ Get Wallet Balance (Jenkins - ₦26,200)
5. ✅ Get Transaction History
6. ✅ Verify Transaction

### **Phase 2: Protected Endpoints (With Auth)**
7. 🔒 Get Business Wallets (KinsTech NG)
8. 🔒 Process Payment (from Jenkins wallet)
9. 🔒 Update BVN
10. 🔒 Transfer to Bank (small amount)
11. 🔒 Create New Wallet (optional)

## 💡 **Testing Tips**

### **Safe Testing:**
- Use Jenkins' wallet (`686c03af17387f9b8f51a95c`) for payments - it has ₦26,200
- Start with small amounts (₦100-500) for transfers
- Test with existing business IDs to avoid errors

### **Expected Behaviors:**
- **Balance checks** should return exact amounts from database
- **Transaction history** will show real past transactions
- **Bank transfers** will interact with real Flutterwave/BANI APIs
- **Payments** will deduct from actual wallet balances

### **Error Testing:**
- Try invalid wallet IDs → 400 Bad Request
- Try without auth token → 401 Unauthorized  
- Try with insufficient balance → Payment will fail

## 🚨 **Important Notes**

⚠️ **This is LIVE DATA** - transactions will affect real balances!  
⚠️ **Bank transfers** will attempt real money transfers  
⚠️ **Start with small amounts** for safety  
⚠️ **Monitor wallet balances** before/after tests

Your wallet service is connected to **production MongoDB** with real user data and balances! 🎯
