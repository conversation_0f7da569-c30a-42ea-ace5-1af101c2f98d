import { NextFunction, Request, Response } from "express";
import kybService from "../services/kycService";
import { User } from "../models/User";
import { WalletService } from "../services/walletService";
import { Business } from "../models/Business";

export class KybController {
  static async addKybData(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user?.id as string;
      const kyBusiness = await kybService.addKybData(req.body, user);
      return res.status(200).json({
        message: "kyb added successfully",
        data: kyBusiness,
      });
    } catch (error: any) {
      next(error); // Forward the error to the error handler
    }
  }

  static async getKybData(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user?.id as string;
      const verifiedBusiness = await kybService.getKybData(user);
      return res.status(200).json({
        message: "Business verified successfully",
        business: verifiedBusiness,
      });
    } catch (error: any) {
      next(error);
    }
  }

  static async updateKybData(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user?.id as string;
      const verifiedBusiness = await kybService.updateKybData(req.body, user);
      return res.status(200).json({
        message: "kyb verified successfully",
        business: verifiedBusiness,
      });
    } catch (error: any) {
      next(error);
    }
  }

  static async getAllKybData(req: Request, res: Response, next: NextFunction) {
    try {
      const verifiedBusiness = await kybService.getAllKybData();
      return res.status(200).json({
        message: "Business verified successfully",
        business: verifiedBusiness,
      });
    } catch (error: any) {
      next(error);
    }
  }

  static async getById(req: Request, res: Response, next: NextFunction) {
    try {
      const verifiedBusiness = await kybService.getById(req.params.id);
      return res.status(200).json({
        message: "Business verified successfully",
        business: verifiedBusiness,
      });
    } catch (error: any) {
      next(error);
    }
  }

  static async verifyKyc(req: Request, res: Response, next: NextFunction) {
    try {
      const { holder_legal_number } = req.body;
      const userId = req.user?.id as string;

      // console.log("finding user", userId);

      // Find user by ID
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      // console.log("user found", user);

      // Call KYC verification service
      const kycData = await kybService.verifyKyc(holder_legal_number);
      // console.log("KYC Data:", kycData);

      if (!kycData || !kycData.status) {
      }

      const { first_name, last_name, middle_name } = kycData;

      const { firstName, lastName, _id, email, phone, legalNumber } =
      user.toObject();

      // console.log("finding business")
      
      const business = await Business.findOne({ primaryEmail: email });

      if (!business) {
        return res.status(400).json({ message: "Business not found" });
      }

      const verificationNames = [first_name, last_name, middle_name].map(
        (name) => name.toLowerCase()
      );

      if (
        !verificationNames.includes(firstName.toLowerCase()) ||
        !verificationNames.includes(lastName.toLowerCase())
      ) {
        return res
        .status(400)
        .json({ message: "KYC verification failed, names do not match" });
      }
      const WalletServiceInstance = new WalletService();
      const wallet = await WalletServiceInstance.createWallet(
        business._id,
        email,
        phone,
        firstName,
        lastName,
        holder_legal_number,
        "narration",
      );
      console.log("created wallet")


      user.legalNumber = holder_legal_number;
      user.kycVerified = true
      await user.save();

      return res.status(200).json({
        message: "KYC verification successful",
        wallet
      });
    } catch (error: any) {
      next(error);
    }
  }
}
