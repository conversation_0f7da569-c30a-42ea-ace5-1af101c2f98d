import { Request, Response } from "express";
import BeneficiaryService from "../services/beneficiary";
import { User } from "../models/User";

export interface CreateBeneficiaryRequest {
  accountNumber: string;
  accountName: string;
  bankName: string;
  businessId: string;
  phoneNumber: string;
}
class BeneficiaryController {
  static async createBeneficiary(req: Request, res: Response) {
    const data: CreateBeneficiaryRequest = req.body;
    try {
      const userId = req.user?.id as string;
      if (!userId) res.status(401).json({ message: "Unauthorized" });

      const result = await BeneficiaryService.createBeneficiary(data, userId);
      if (result)
        res
          .status(201)
          .json({ message: "Beneficiary created successfully", data: result });
    } catch (error: any) {
      throw new Error(error);
    }
  }

  static async getBeneficiaries(req: Request, res: Response) {
    try {
      const userId = req.user?.id as string;
      if (!userId) res.status(401).json({ message: "Unauthorized" });

      const result = await BeneficiaryService.getBeneficiaries(userId);
      return res
        .status(200)
        .json({
          message: "Beneficiaries retrieved successfully",
          data: result,
        });
    } catch (error: any) {
      throw new Error(error);
    }
  }


static async userWalletDetails(req: Request, res: Response) {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({ message: "Email is required" });
        }
        
        const user = await User.findOne({ email });
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }
        
        const accountName = await BeneficiaryService.userWalletDetails(email);
        if (!accountName) {
            return res.status(404).json({ message: "No wallet found for this user" });
        }
        
        return res.status(200).json({
            message: "User wallet details retrieved successfully",
            data: accountName
        });
    } catch (error: any) {
        return res.status(500).json({ 
            message: "Internal server error",
            error: error.message 
        });
    }
}

  static async getBeneficiaryById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const result = await BeneficiaryService.getBeneficiaryById(id);
      if (result)
        res
          .status(200)
          .json({
            message: "Beneficiary retrieved successfully",
            data: result,
          });
      else res.status(404).json({ message: "Beneficiary not found" });
    } catch (error: any) {
      throw new Error(error);
    }
  }

  static async deleteBeneficiary(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id as string;
      const result = await BeneficiaryService.deleteBeneficiary(id, userId);
      if (result)
        res.status(200).json({ message: "Beneficiary deleted successfully" });
    } catch (error: any) {
      throw new Error(error);
    }
  }
}

export default BeneficiaryController;
