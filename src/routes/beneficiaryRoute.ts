import { Router } from "express";
import BeneficiaryController from "../controllers/beneficiaryController";
import { protect } from "../middlewares/auth";
import { validate } from "../middlewares/validate";
import { createBeneficiary } from "../schemas/beneficiary";

const router = Router()

router.post("/create", protect, validate({body: createBeneficiary}), BeneficiaryController.createBeneficiary)
router.get("/", protect, BeneficiaryController.getBeneficiaries)
router.post("/wallet", protect, BeneficiaryController.userWalletDetails)
router.get("/:id", BeneficiaryController.getBeneficiaryById)
router.delete("/:id", BeneficiaryController.deleteBeneficiary) 

/**
 * @swagger
 * tags:
 *   name: Beneficiaries
 *   description: API for managing beneficiaries
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Beneficiary:
 *       type: object
 *       required:
 *         - accountName
 *         - accountNumber
 *         - bankName
 *         - businessId
 *       properties:
 *         accountName:
 *           type: string
 *           description: Name of the account holder
 *         accountNumber:
 *           type: string
 *           description: Bank account number
 *         bankName:
 *           type: string
 *           description: Name of the bank
 *         businessId:
 *           type: string
 *           description: ID of the associated business
 *         userId:
 *           type: string
 *           description: ID of the user who created the beneficiary
 *       example:
 *         accountName: John Doe
 *         accountNumber: **********
 *         bankName: Example Bank
 *         businessId: 5f8d04b3ab35a642d4c8d9d2
 * 
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *           description: Error message
 *       example:
 *         message: An error occurred
 */

/**
 * @swagger
 * /api/v1/beneficiaries/create:
 *   post:
 *     summary: Create a new beneficiary
 *     tags: [Beneficiaries]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Beneficiary'
 *     responses:
 *       201:
 *         description: Beneficiary created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Beneficiary'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/v1/beneficiaries:
 *   get:
 *     summary: Get all beneficiaries for the authenticated user
 *     tags: [Beneficiaries]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of beneficiaries
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Beneficiary'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/v1/beneficiaries/wallet:
 *   post:
 *     summary: Get wallet details for a user
 *     description: Retrieves the business bank name associated with a user's wallet using their email
 *     tags: [Wallet]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email of the user to fetch wallet details for
 *     responses:
 *       200:
 *         description: Wallet details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "User wallet details retrieved successfully"
 *                 data:
 *                   type: string
 *                   description: The business bank name or a message if no wallet found
 *                   example: "Chase Bank"
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "User not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/v1/beneficiaries/{id}:
 *   get:
 *     summary: Get a beneficiary by ID
 *     tags: [Beneficiaries]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Beneficiary ID
 *     responses:
 *       200:
 *         description: Beneficiary data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Beneficiary'
 *       404:
 *         description: Beneficiary not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 * 
 *   delete:
 *     summary: Delete a beneficiary
 *     tags: [Beneficiaries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Beneficiary ID
 *     responses:
 *       200:
 *         description: Beneficiary deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

export default router