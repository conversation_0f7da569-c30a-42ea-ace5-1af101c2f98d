import express from "express";
import Auth<PERSON>ontroller from "../controllers/authController";
import { validate } from "../middlewares/validate";
import {
  registerSchema,
  loginSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
} from "../schemas/authSchema";
import { User } from "../models/User";
import { Wallet } from "../models/Wallet";

const router = express.Router();

/**
 * @swagger
 * /api/v1/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterUser'
 *     responses:
 *       201:
 *         description: Registration successful
 *       400:
 *         description: User already exists or invalid data
 *       500:
 *         description: Internal server error
 */
router.post(
  "/register",
  validate({ body: registerSchema }),  
  AuthController.register
);

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginUser'  # Reference from swagger.ts
 *     responses:
 *       200:
 *         description: Login successful, returns JWT token
 *       401:
 *         description: Invalid credentials
 *       500:
 *         description: Internal server error
 */
router.post("/login", validate({ body: loginSchema }), AuthController.login);

/**
 * @swagger
 * /api/v1/auth/reset-password:
 *   post:
 *     summary: Reset user password
 *     description: Resets the user's password using a valid reset token.
 *     tags:
 *       - Authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ResetPasswordRequest'
 *     responses:
 *       200:
 *         description: Password reset successful.
 *       400:
 *         description: Invalid or expired token.
 *       500:
 *         description: Server error.
 */
router.post(
  "/reset-password",
  validate({ body: resetPasswordSchema }),
  AuthController.resetPassword
);

router.post("/add-business-account", async (req, res) => {
  try {
    const result = await Wallet.updateMany(
      {},
      { $set: { business_account_number: "", business_bank_name: "" } }
    );

    res.status(200).json({
      message: "kycVerified field added to users",
      matchedCount: result.matchedCount,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Error updating users:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

export default router;
