import { Router } from "express";
import { KybController } from "../controllers/kybController";
import { adminProtect, protect } from "../middlewares/auth";
import { validate } from "../middlewares/validate";
import { registerKybSchema } from "../schemas/kybSchema";

const router = Router()

/**
 * @swagger
 * /api/v1/kyc/add:
 *   post:
 *     summary: Register KYB
 *     description: Add KYB (Know Your Business) data for a user.
 *     tags: [KYB]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: "#/components/schemas/Kyb"
 *     responses:
 *       201:
 *         description: Business registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Business registered successfully
 *                 data:
 *                   $ref: "#/components/schemas/Kyb"
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Validation error
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["regNumber is required", "taxId must be a valid string"]
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unauthorized
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: An error occurred while registering KYB data
 *
 * components:
 *   schemas:
 *     Kyb:
 *       type: object
 *       required:
 *         - regNumber
 *         - taxId
 *         - proofAdd
 *         - uploadCac
 *         - uploadMoc
 *         - govPersonalId
 *         - uploadUtilityBill
 *         - phoneNumber
 *       properties:
 *         regNumber:
 *           type: string
 *           description: Business registration number
 *           example: "BN123456789"
 *         taxId:
 *           type: string
 *           description: Tax identification number
 *           example: "TIN987654321"
 *         proofAdd:
 *           type: string
 *           description: Proof of address document (e.g., URL or file path)
 *           example: "https://example.com/proof_address.pdf"
 *         uploadCac:
 *           type: string
 *           description: Uploaded CAC (Corporate Affairs Commission) document (e.g., URL or file path)
 *           example: "https://example.com/cac_document.pdf"
 *         uploadMoc:
 *           type: string
 *           description: Uploaded Memorandum of Association document (e.g., URL or file path)
 *           example: "https://example.com/moc_document.pdf"
 *         govPersonalId:
 *           type: string
 *           description: Government-issued personal ID of the business representative
 *           example: "ID123456789"
 *         uploadUtilityBill:
 *           type: string
 *           description: Uploaded utility bill document (e.g., URL or file path)
 *           example: "https://example.com/utility_bill.pdf"
 *         phoneNumber:
 *           type: string
 *           description: Business contact phone number
 *           example: "+2341234567890"
 *         user:
 *           type: string
 *           description: MongoDB ObjectId of the associated user (optional)
 *           example: "507f1f77bcf86cd799439011"
 *           nullable: true 
 */
router.post("/add", protect, validate({body: registerKybSchema}), KybController.addKybData)

/**
 * @swagger 
 * /api/v1/business:
 *   get:
 *     summary: Get kyb belonging to a user
 *     description: Retrieves kyb businesses associated with the authenticated user.
 *     tags: [KYB]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: User kyb retrieved successfully
 *       400:
 *         description: User ID is required
 *       500:
 *         description: Server error
 */

router.get("/", protect, KybController.getKybData)

/**      
 * @swagger
 * /api/v1/kyb/all:
 *   get:
 *     summary: Get all businesses kyb for users
 *     description: Retrieves all kyb associated with users.
 *     tags: [KYB]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Kyb retrieved successfully
 *       400:
 *         description: User ID is required
 *       500:
 *         description: Server error
 */


router.get("/all", adminProtect, KybController.getAllKybData)


/**
 * @swagger
 * /api/v1/kyb/update:
 *   patch:
 *     summary: Update KYB data
 *     description: Update user Know Your Business (KYB) data with partial updates.
 *     tags: [KYB]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/KybUpdate'
 *     responses:
 *       200:
 *         description: KYB data updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: KYB data updated successfully
 *                 data:
 *                   $ref: '#/components/schemas/Kyb'
 *       400:
 *         description: Validation error or missing user ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Validation error
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ['phoneNumber must be a valid phone number']
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unauthorized
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: An error occurred while updating KYB data
 *
 * components:
 *   schemas:
 *     Kyb:
 *       type: object
 *       required:
 *         - regNumber
 *         - taxId
 *         - proofAdd
 *         - uploadCac
 *         - uploadMoc
 *         - govPersonalId
 *         - uploadUtilityBill
 *         - phoneNumber
 *       properties:
 *         regNumber:
 *           type: string
 *           description: Business registration number
 *           example: BN123456789
 *         taxId:
 *           type: string
 *           description: Tax identification number
 *           example: TIN987654321
 *         proofAdd:
 *           type: string
 *           description: Proof of address document (e.g., URL or file path)
 *           example: https://example.com/proof_address.pdf
 *         uploadCac:
 *           type: string
 *           description: Uploaded CAC (Corporate Affairs Commission) document
 *           example: https://example.com/cac_document.pdf
 *         uploadMoc:
 *           type: string
 *           description: Uploaded Memorandum of Association document
 *           example: https://example.com/moc_document.pdf
 *         govPersonalId:
 *           type: string
 *           description: Government-issued personal ID of the business representative
 *           example: ID123456789
 *         uploadUtilityBill:
 *           type: string
 *           description: Uploaded utility bill document
 *           example: https://example.com/utility_bill.pdf
 *         phoneNumber:
 *           type: string
 *           description: Business contact phone number
 *           example: +2341234567890
 *         user:
 *           type: string
 *           description: MongoDB ObjectId of the associated user
 *           example: 507f1f77bcf86cd799439011
 *           nullable: true
 *     KybUpdate:
 *       type: object
 *       properties:
 *         regNumber:
 *           type: string
 *           description: Business registration number
 *           example: BN123456789
 *         taxId:
 *           type: string
 *           description: Tax identification number
 *           example: TIN987654321
 *         proofAdd:
 *           type: string
 *           description: Proof of address document (e.g., URL or file path)
 *           example: https://example.com/proof_address.pdf
 *         uploadCac:
 *           type: string
 *           description: Uploaded CAC (Corporate Affairs Commission) document
 *           example: https://example.com/cac_document.pdf
 *         uploadMoc:
 *           type: string
 *           description: Uploaded Memorandum of Association document
 *           example: https://example.com/moc_document.pdf
 *         govPersonalId:
 *           type: string
 *           description: Government-issued personal ID of the business representative
 *           example: ID123456789
 *         uploadUtilityBill:
 *           type: string
 *           description: Uploaded utility bill document
 *           example: https://example.com/utility_bill.pdf
 *         phoneNumber:
 *           type: string
 *           description: Business contact phone number
 *           example: +2341234567890
 *         user:
 *           type: string
 *           description: MongoDB ObjectId of the associated user
 *           example: 507f1f77bcf86cd799439011
 *           nullable: true
 */
router.patch("/update", protect, KybController.updateKybData)

router.post("/verify-kyc", protect, KybController.verifyKyc)

/**
 * @swagger
 * /api/v1/kyc/{id}:
 *   get:
 *     summary: Get kyb by ID
 *     description: Retrieves all businesses data.
 *     tags: [KYB]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           description: The ID of the KYB to retrieve
 *           example: 612345678901234567890123         
 *     responses:
 *       200:
 *         description: KYB retrieved successfully
 *       400:
 *         description: User ID is required
 *       500:
 *         description: Server error
 */

router.get("/:id", protect, KybController.getById)
export default router;