import { model, Schema } from "mongoose";

interface Beneficiary{
    accountName: string;
    accountNumber: string;
    bankName: string;
    businessId: Schema.Types.ObjectId;
    userId: Schema.Types.ObjectId;
    phoneNumber: string;
}

const beneficiarySchema = new Schema<Beneficiary>({
    accountName: { type: String, required: true },
    accountNumber: { type: String, required: true },
    bankName: { type: String, required: true },
    businessId: { type: Schema.Types.ObjectId, ref:"Business", required: true },
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    phoneNumber: {type: String, required: true }      
}, {timestamps: true});

const Beneficiary = model("Beneficiary", beneficiarySchema);

export default Beneficiary;  