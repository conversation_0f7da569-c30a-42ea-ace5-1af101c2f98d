import { model, ObjectId, Schema } from "mongoose";

 export interface kybDocs {
    regNumber: string;
    taxId: string;
    proofAdd: string;
    uploadCac: string;
    uploadMoc: string
    govPersonalId: string;
    uploadUtilityBill: string;
    phoneNumber: string;
    user?: ObjectId;
    businessId: ObjectId;
    businessName?: string
  }

  const kybSchema = new Schema<kybDocs>({
    regNumber: { type: String, required: true },
    taxId: { type: String, required: true },
    proofAdd: { type: String, required: false },
    uploadCac: { type: String, required: false },
    uploadMoc: { type: String, required: false },
    govPersonalId: { type: String, required: false },
    uploadUtilityBill: { type: String, required: false },
    phoneNumber: { type: String, required: true} ,
    user: {type: Schema.Types.ObjectId, ref: 'User'},  
    businessId: {type: Schema.Types.ObjectId, ref: 'Business'},
    businessName: {type: String, required: false }
  }, {timestamps: true});

  const kybModel = model<kybDocs>('kyb', kybSchema);

  export default kybModel;