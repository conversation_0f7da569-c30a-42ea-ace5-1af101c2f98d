import {z} from 'zod';

export const createBeneficiary = z.object({
    bankName: z.string().min(3, {message: 'Bank name must be at least 3 characters long'}),
    accountNumber: z.string().min(10, {message: 'Account number must be at least 10 characters long'}),
    accountName: z.string().min(3, {message: 'Account name must be at least 3 characters long'}),
    businessId: z.string().min(3, {message: 'Business ID must be at least 3 characters long'}),
    phoneNumber: z.string().min(10, {message: 'Phone number must be at least 10 characters long'}),
}) 