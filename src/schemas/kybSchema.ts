import { z } from "zod";

 export interface kybDocs {
    regNumber: string;
    taxId: string;
    proofAdd: string;
    uploadCac: string;
    uploadMoc: string
    govPersonalId: string;
    uploadUtilityBill: string;
    phoneNumber: string;
    businessName: string;
  }

// Schema for registering a business
export const registerKybSchema = z.object({
  regNumber: z
    .string()
    .min(2, "Registration number must be at least 2 characters"),
    proofAdd: z.string().min(2, "Logo must be a valid URL"),
    taxId: z.string().min(4, "Tax ID must be at least 4 characters"),
    uploadCac: z.string().min(2, "uploaded CAC must be at least 2 characters"),
    uploadMoc: z.string().min(2, "Uploaded moc must be at least 2 characters"),
    phoneNumber: z.string().min(10, "Type must be at least 10 characters"),
    businessName: z.string().min(2, "Business name is required"),
    uploadUtilityBill: z.string().min(1, "Utility bill is required"),
    govPersonalId: z.string().min(1, "Gov Personal ID is required"),
    businessId: z.string().min(1, "Business ID is required"),

}); 