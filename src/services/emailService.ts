import axios, { AxiosError } from "axios";
import { env } from "../utils/envValidator";

export class EmailService {
  private static zoho_key = env.ZOHO_KEY;
  private static gate_zoho_key = env.G_ZOHO_KEY;
  private static apiUrl = env.ZOHO_URL;

  private static coconutConfig = {
    headers: { 
      "Content-Type": "application/json",    
      Authorization: `${this.zoho_key}`, 
    },
  };

  private static gateConfig = {
    headers: { 
      "Content-Type": "application/json",    
      Authorization: `${this.gate_zoho_key}`, 
    },
  };

  static async alertAdminOfShipment() {
    const payload = {
      from: { address: "<EMAIL>", name: "Coconut Africa" },
      to: [
        { email_address: { address: '<EMAIL>', name: "Temitope" } },
        { email_address: { address: '<EMAIL>', name: "<PERSON><PERSON><PERSON><PERSON><PERSON>" } }
      ],
      template_key: "shipment-submitted"
    };

    try{

        console.log("alerting admins")
       await axios.post(this.apiUrl, payload, this.coconutConfig);
    } catch(error: any){
        console.log("failed to alert admins", error?.response?.data || error.message || error.data);
    }
  }

  
  static async alertUserOfWaybill(email: string, route: string, name: string, address: string) {
    const payload = {
      from: route === "gate" ? { address: "<EMAIL>", name: "Gate Logistics" } : { address: "<EMAIL>", name: "Coconut Africa" },
      to: [
        { email_address: { address: email, name: name} },
      ],
      template_key: "waybill-booked",
      merge_info: {
        name: name,
        address: address,
      }
    };

    const config = route === "gate" ? this.gateConfig : this.coconutConfig;

    try{

        console.log("alerting users")
       await axios.post(this.apiUrl, payload, config);
    } catch(error: any){
        console.log("failed to alert users", error?.response?.data.error?.details || error.message || error.data);
    }
  }

  
}
