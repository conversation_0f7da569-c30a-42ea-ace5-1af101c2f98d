import { CreateBeneficiaryRequest } from "../controllers/beneficiaryController";
import Beneficiary from "../models/Beneficiary";
import { Wallet } from "../models/Wallet";

class BeneficiaryService {
  static async createBeneficiary(
    data: CreateBeneficiaryRequest,
    userId: string
  ) {
    try {
      const beneficiary = await Beneficiary.create({ ...data, userId });
      return beneficiary;
    } catch (error: any) {
      throw new Error(error);
    }
  }

  static async getBeneficiaries(userId: string) {
    try {
      console.log(userId);
      const beneficiaries = await Beneficiary.find({ userId });
      return beneficiaries;
    } catch (error: any) {
      throw new Error(error);
    }
  }

  static async getBeneficiaryById(beneficiaryId: string) {
    try {
      const beneficiary = await Beneficiary.findById(beneficiaryId);
      return beneficiary;
    } catch (error: any) {
      throw new Error(error);
    }
  }

  static async userWalletDetails(email: string) {
    try {
      const wallet = await Wallet.findOne({ email });
      if (wallet) {
        const accountName = wallet.business_bank_name;   
        return accountName;
      } else {
        return { message: "No wallet found for this user" };
      }
    } catch (error:any) {
      throw new Error(error);
    }
  }

  static async deleteBeneficiary(beneficiaryId: string, userId: string) {
    try {
      // const user = await Beneficiary.findOne({user: userId})
      const beneficiary = await Beneficiary.findOneAndDelete({
        user: userId,
        _id: beneficiaryId,
      });
      if (!beneficiary)
        throw new Error("You are not authorized to delete this beneficiary");
      return { message: "Beneficiary deleted successfully" };
    } catch (error: any) {
      throw new Error(error);
    }
  }
}

export default BeneficiaryService;
