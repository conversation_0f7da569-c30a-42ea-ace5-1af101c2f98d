import axios from "axios";
import kybModel, { kybDocs } from "../models/Kyb";
import { env } from "../utils/envValidator";
import { Business } from "../models/Business";

class kybService {
  private static baniBaseUrl = env.BANI_BASE_URL;
  private static moniSignature = env.BANI_MONI_SIGNATURE;
  private static baniToken = env.BANI_TOKEN;

  static addKybData = async (kybData: kybDocs, user: string) => {
    const {
      govPersonalId,
      phoneNumber,
      proofAdd,
      regNumber,
      taxId,
      uploadCac,
      uploadMoc,
      uploadUtilityBill,
      businessId
    } = kybData;
    try {
      const kybExist = await kybModel.findOne({ user });
      if (kybExist) {
        throw new Error("KYB already exist");
      }
      const business = await Business.findById(businessId);
      if (!business) {
        throw new Error("Business not found");
      }
      const kyb = new kybModel({
        govPersonalId,
        phoneNumber,
        proofAdd,
        regNumber,
        taxId,
        uploadCac,
        uploadMoc,
        uploadUtilityBill,
        user,
        businessId,
        businessName: business.businessName,
      });
      await kyb.save();
      return { message: "KYB added successfully" };
    } catch (error: any) {
      throw new Error(error.message);
    }
  };

  static getKybData = async (user: string) => {
    try {
      const kyb = await kybModel.findOne({ user });
      if (!kyb) {
        throw new Error("KYB not found");
      }
      return kyb;
    } catch (error: any) {
      throw new Error(error.message);
    }
  };

  static updateKybData = async (kybData: kybDocs, user: string) => {
    const {
      govPersonalId,
      phoneNumber,
      proofAdd,
      regNumber,
      taxId,
      uploadCac,
      uploadMoc,
      uploadUtilityBill,
    } = kybData;
    try {
      const kyb = await kybModel.findOne({ user });
      if (!kyb) {
        throw new Error("KYB not found");
      }
      const updatedKyb = await kybModel.findOneAndUpdate(
        { user },
        {
          govPersonalId,
          phoneNumber,
          proofAdd,
          regNumber,
          taxId,
          uploadCac,
          uploadMoc,
          uploadUtilityBill,
        },
        { new: true }
      );
      return updatedKyb;
    } catch (error: any) {
      throw new Error(error.message);
    }
  };

  static getAllKybData = async () => {
    try {
      const kyb = await kybModel.find();
      if (!kyb) {
        // throw new Error("KYB not found");
        return { status: true, data: []};
      }
      return kyb;
    } catch (error: any) {   
      throw new Error(error.message);
    }
  };

  static getById = async (id: string) => {
    try {
      const kyb = await kybModel.findById(id);
      if (!kyb) {
        throw new Error("KYB not found");
      }
      return kyb;
    } catch (error: any) {
      throw new Error(error.message);
    }
  };

  static verifyKyc = async (holder_legal_number: string) => {
    try {
      const url = `${this.baniBaseUrl}partner/verification/legal_number/`;
      const payload = {
        holder_legal_number,
      };
      console.log("Payload for KYC verification:", payload);
      const response = await axios.post(url, payload, 
        {
          headers: { 
            Authorization: `Bearer ${this.baniToken}`,
            "Content-Type": "application/json",
            "moni-signature": this.moniSignature,
          },
        }
       )
      ;
      console.log(" KYC verification done:", response.data);

      return response.data;

    } catch (error: any) {
      console.log(error?.response?.data)
      throw new Error(error.response?.data?.message || error.message);
    }

  };

}

export default kybService;
