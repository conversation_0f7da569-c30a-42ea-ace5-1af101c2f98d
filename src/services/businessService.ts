import { Business } from "../models/Business";
import { GBusiness } from "../models/GBusiness";
import { GUser } from "../models/GUsers";
import { User } from "../models/User";

class BusinessService {
  // Register a new business
  static async registerBusiness(userId: string, businessData: any) {
    const { businessName } = businessData;

    // Check if business already exists
    const existingBusiness = await Business.findOne({ businessName });
    if (existingBusiness) {
      throw new Error("Business already exists");
    }

    const newBusiness = new Business({ userId, ...businessData });
    await newBusiness.save();
    return newBusiness;
  }

  // Update business details
  static async updateBusiness(businessId: string, updateData: {isRegistered: boolean}) {
    const updatedBusiness = await Business.findByIdAndUpdate(
      businessId,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedBusiness) {
      throw new Error("Business not found");
    }

    return updatedBusiness;
  }

  // Get a business by ID
  static async getBusiness(businessId: string) {
    const business = await Business.findById(businessId);
    if (!business) {
      throw new Error("Business not found");
    }

    return business;
  }
  // Get a business by ID
  static async getBusinessAndPopulateUser(businessId: string) {
    let business = await Business.findById(businessId)
    let route: string = "coconut"
    if (!business) {

      business = await GBusiness.findById(businessId)


      if (!business) {
        throw new Error("Business not found");
      }
      route = "gate"
    }

    let user = await User.findById(business.userId)

    if (!user) {
      user = await GUser.findById(business.userId);
    }

    return {name: user?.firstName || "" , email: user?.email || "", route};

  }


  // Get all businesses belonging to a user
  static async getUserBusinesses(userId: string) {
    return await Business.find({ userId });
  }

  static async verifyBusiness(businessId: string) {
    const business = await Business.findByIdAndUpdate(
      businessId,
      { isRegistered: true },
      { new: true }
    );

    if (!business) {
      throw new Error("Business not found");
    }

    return business;
  }

  static async fetchBusinessData(){
    return await Business.find();
  }
}

export default BusinessService;
