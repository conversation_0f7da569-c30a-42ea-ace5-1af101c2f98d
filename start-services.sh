#!/bin/bash

echo "🚀 Starting Coconut Backend Services..."

# Start the main application
echo "📱 Starting main application on port 8000..."
npm run dev &
MAIN_PID=$!

# Wait a moment for the main app to start
sleep 2

# Start the wallet service
echo "💰 Starting wallet service on port 8001..."
cd wallet-service
npm run dev &
WALLET_PID=$!

# Go back to root directory
cd ..

echo "✅ Services started successfully!"
echo "📱 Main Application: http://localhost:8000"
echo "💰 Wallet Service: http://localhost:8001"
echo "🔍 Wallet Service Health: http://localhost:8001/health"

# Function to handle cleanup
cleanup() {
    echo "🛑 Stopping services..."
    kill $MAIN_PID 2>/dev/null
    kill $WALLET_PID 2>/dev/null
    echo "✅ Services stopped."
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Wait for both processes
wait $MAIN_PID $WALLET_PID
