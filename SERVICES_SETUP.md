# Coconut Backend Services Setup

This document explains how to run the separated wallet service alongside the main application.

## Architecture Overview

The application has been successfully separated into two independent services:

1. **Main Application** - Handles all business logic except wallet operations
2. **Wallet Service** - Independent microservice handling all wallet-related operations

## Services

### 1. Main Application
- **Port**: 8000
- **Base URL**: `http://localhost:8000`
- **Description**: Handles authentication, business management, shipping, filling, packaging, etc.

### 2. Wallet Service
- **Port**: 8001
- **Base URL**: `http://localhost:8001`
- **Health Check**: `http://localhost:8001/health`
- **API Base**: `http://localhost:8001/api/v1/wallet`
- **Description**: Independent wallet microservice with payment processing, bank transfers, and transaction management

## Running the Services

### Option 1: Using the Start Script (Recommended)
```bash
./start-services.sh
```

This script will start both services simultaneously:
- Main application on port 8000
- Wallet service on port 8001

### Option 2: Manual Start

#### Start Wallet Service
```bash
cd wallet-service
npm install
npm run dev
```

#### Start Main Application (in another terminal)
```bash
# From root directory
npm install
npm run dev
```

## Wallet Service API Endpoints

### Base URL: `http://localhost:8001/api/v1/wallet`

- `GET /health` - Health check
- `POST /` - Create a new wallet
- `GET /banks` - Get all available banks
- `POST /pay` - Process payment from wallet
- `POST /bani` - Transfer money via Bani
- `POST /transfer/:walletId` - Transfer money via Flutterwave
- `GET /transactions/:walletId` - Get wallet transactions
- `GET /verify/:transactionId` - Verify a transaction
- `PATCH /bvn` - Update wallet BVN
- `GET /business/:businessId` - Get business wallets
- `GET /balance/:walletId` - Get wallet balance

## Testing the Services

### Test Wallet Service Health
```bash
curl http://localhost:8001/health
```

### Test Banks Endpoint
```bash
curl http://localhost:8001/api/v1/wallet/banks
```

## Environment Variables

Both services use the same MongoDB database and share common environment variables. The wallet service has its own `.env` file with the necessary configurations.

### Wallet Service Environment Variables
- `PORT=8001`
- `MONGO_URI` - MongoDB connection string
- `JWT_SECRET` - JWT secret key
- `FLUTTERWAVE_BASE_URL` - Flutterwave API URL
- `FLUTTERWAVE_SECRET_KEY` - Flutterwave secret key
- `BANI_BASE_URL` - Bani API URL
- `BANI_TOKEN` - Bani authentication token
- `BANI_MONI_SIGNATURE` - Bani signature
- `BANI_SHARED_KEY` - Bani shared key
- Wallet IDs for different services

## Database

Both services connect to the same MongoDB database, ensuring data consistency across the application.

## Authentication

The wallet service uses JWT authentication for protected endpoints. Tokens issued by the main application are valid for the wallet service as they share the same JWT secret.

## Benefits of Separation

1. **Independent Scaling**: Wallet service can be scaled independently based on payment processing needs
2. **Fault Isolation**: Issues in wallet service don't affect other business operations
3. **Technology Flexibility**: Each service can use different technologies if needed
4. **Team Autonomy**: Different teams can work on different services
5. **Deployment Independence**: Services can be deployed separately

## Monitoring

- Main Application: Monitor on port 8000
- Wallet Service: Monitor on port 8001 with health check endpoint

## Next Steps

1. Set up proper logging and monitoring for both services
2. Implement service discovery if deploying to production
3. Add API gateway for unified access point
4. Set up proper CI/CD pipelines for each service
5. Implement proper error handling and retry mechanisms
